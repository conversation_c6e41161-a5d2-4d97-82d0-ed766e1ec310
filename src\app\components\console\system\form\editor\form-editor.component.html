<!-- form-js 表单编辑器 -->
<div class="form-editor-wrapper">
    <!-- 顶部工具栏 -->
    <div class="form-toolbar">
        <div class="toolbar-left">
            <button nz-button nzType="text" (click)="goBack()" class="back-btn">
                <i nz-icon nzType="arrow-left"></i>
                返回
            </button>
            <span class="divider">|</span>
            <span class="form-title">
                {{ formData?.name || '新建表单' }}
            </span>
        </div>

        <div class="toolbar-right">
            <button nz-button nzType="default" (click)="previewForm()" nz-tooltip="预览表单">
                <i nz-icon nzType="eye"></i>
                预览
            </button>
            <button nz-button nzType="default" (click)="downloadConfig()" nz-tooltip="下载配置" style="margin-left: 8px;">
                <i nz-icon nzType="download"></i>
                配置
            </button>
            <button nz-button
                    nzType="primary"
                    (click)="save()"
                    style="margin-left: 16px;"
                    [disabled]="saving">
                <i nz-icon nzType="save" [nzSpin]="saving"></i>
                {{ saving ? '保存中...' : '保存' }}
            </button>
        </div>
    </div>

    <!-- form-js 编辑器容器 -->
    <div class="form-editor-container" [class.loading]="loading">
        <div #formContainer class="form-js-container"></div>

        <!-- 加载遮罩 -->
        <div class="loading-mask" *ngIf="loading">
            <nz-spin nzSize="large" nzTip="加载中..."></nz-spin>
        </div>
    </div>
</div>
