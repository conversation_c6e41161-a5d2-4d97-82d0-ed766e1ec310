import { NgModule, NgModuleRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SystemRoutingModule } from './system-routing.module';
import { ManagementComponent } from '../../../components/console/system/management/management.component';
import { SharedModule } from '../../shared/shared.module';
import { HmrModuleHelper } from 'src/app/hmr-module-helper';
import {AccountComponent} from "../../../components/console/system/account/account.component";
import {AccountEditComponent} from "../../../components/console/system/account/edit/edit.component";
import {OrderComponent} from "../../../components/console/system/order/order.component";
import { IndexComponent as PermissionIndexComponent } from 'src/app/components/console/system/permission/index/index.component';
import { QuotaComponent } from 'src/app/components/console/system/quota/quota.component';
import { DetailComponent } from 'src/app/components/console/system/quota/detail/detail.component';
import { UserTemplateComponent } from 'src/app/components/console/system/quota/user-template/user-template.component';
import { AppSystemQuotaComponent } from 'src/app/components/console/system/quota/app-system-quota/app-system-quota.component';
import { CloudAuditComponent } from 'src/app/components/console/monitor-manager/cloud-audit/cloud-audit.component';
import { IndexComponent as ServicePlanIndexComponent } from 'src/app/components/console/service-plan/index/index.component';
import { EditComponent as ServicePlanEditComponent } from 'src/app/components/console/service-plan/edit/edit.component';
import { ServicePlanComponent } from 'src/app/components/console/system/servicePlan/service-plan.component';
import { ServicePlanConfigComponent } from 'src/app/components/console/system/servicePlan/config/service-plan-config.component';
import { BpmnComponent } from 'src/app/components/console/system/bpmn/bpmn.component';
import { BpmnConfigComponent } from 'src/app/components/console/system/bpmn/config/bpmn-config.component';
import { BpmnEditorComponent } from 'src/app/components/console/system/bpmn/editor/bpmn-editor.component';
import { BpmnViewComponent } from 'src/app/components/console/system/bpmn/view/bpmn-view.component';
import { FormComponent } from 'src/app/components/console/system/form/form.component';

@NgModule({
    declarations: [
        ManagementComponent,
        AccountComponent,
        AccountEditComponent,
        OrderComponent,
        PermissionIndexComponent,
        QuotaComponent,
        DetailComponent,
        UserTemplateComponent,
        AppSystemQuotaComponent,
        CloudAuditComponent,
        ServicePlanIndexComponent,
        ServicePlanEditComponent,
        ServicePlanComponent,
        ServicePlanConfigComponent,
        BpmnComponent,
        BpmnConfigComponent,
        BpmnEditorComponent,
        BpmnViewComponent,
        FormComponent
    ],
    imports: [CommonModule, SharedModule, SystemRoutingModule]
})
export class SystemModule {
    constructor(moduleRef: NgModuleRef<SystemModule>) {
        HmrModuleHelper.enableHmrRouterNgModule(module, moduleRef);
    }
}

HmrModuleHelper.enableHmrNodeModule(module);
