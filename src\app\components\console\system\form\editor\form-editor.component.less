// 嵌入式表单编辑器样式
.form-editor-embedded {
  height: calc(100vh - 20px);
  min-height: 600px;
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;

  // 顶部工具栏
  .form-toolbar {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 8px;
        
        &:hover {
          color: #1890ff;
        }
      }

      .divider {
        margin: 0 12px;
        color: #d9d9d9;
        font-size: 14px;
      }

      .form-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  // 编辑器容器
  .form-editor-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 500px;

    &.loading {
      pointer-events: none;
    }

    .form-main-content {
      display: flex;
      width: 100%;
      height: 100%;

      // 左侧组件面板
      .components-panel {
        width: 250px;
        height: 100%;
        border-right: 1px solid #e8e8e8;
        background: #fafafa;
        overflow: auto;
        flex-shrink: 0;

        .panel-header {
          padding: 12px 16px;
          border-bottom: 1px solid #e8e8e8;
          background: #fff;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }
        }

        .panel-body {
          padding: 16px;

          .component-group {
            margin-bottom: 24px;

            &:last-child {
              margin-bottom: 0;
            }

            .group-title {
              font-size: 12px;
              font-weight: 500;
              color: #8c8c8c;
              margin-bottom: 8px;
              text-transform: uppercase;
            }

            .component-list {
              .component-item {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                margin-bottom: 4px;
                background: #fff;
                border: 1px solid #e8e8e8;
                border-radius: 4px;
                cursor: grab;
                transition: all 0.3s;

                &:hover {
                  border-color: #1890ff;
                  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
                }

                &:active {
                  cursor: grabbing;
                }

                i {
                  margin-right: 8px;
                  color: #1890ff;
                  font-size: 14px;
                }

                span {
                  font-size: 12px;
                  color: #262626;
                }
              }
            }
          }
        }
      }

      // 中间表单设计区域
      .form-design-area {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        background: #f5f5f5;

        .design-header {
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 16px;
          border-bottom: 1px solid #e8e8e8;
          background: #fff;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }

          .design-actions {
            display: flex;
            align-items: center;

            button {
              margin-left: 4px;
              
              &:hover {
                color: #1890ff;
              }
            }
          }
        }

        .form-canvas {
          flex: 1;
          padding: 20px;
          overflow: auto;
          background: #fff;
          margin: 16px;
          border-radius: 6px;
          border: 1px solid #e8e8e8;
          min-height: 400px;

          // 表单预览样式
          .form-preview {
            max-width: 600px;
            margin: 0 auto;

            .form-header {
              margin-bottom: 24px;
              text-align: center;

              h3 {
                margin: 0 0 8px 0;
                font-size: 20px;
                font-weight: 500;
                color: #262626;
              }

              p {
                margin: 0;
                font-size: 14px;
                color: #8c8c8c;
              }
            }

            .form-body {
              .form-field {
                margin-bottom: 16px;

                label {
                  display: block;
                  margin-bottom: 4px;
                  font-size: 14px;
                  font-weight: 500;
                  color: #262626;

                  .required {
                    color: #ff4d4f;
                    margin-left: 2px;
                  }
                }

                input, textarea, select {
                  width: 100%;
                  padding: 8px 12px;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  font-size: 14px;
                  transition: border-color 0.3s;

                  &:focus {
                    border-color: #1890ff;
                    outline: none;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                  }

                  &::placeholder {
                    color: #bfbfbf;
                  }
                }

                textarea {
                  resize: vertical;
                  min-height: 80px;
                }
              }
            }

            .form-footer {
              margin-top: 24px;
              text-align: center;

              .btn {
                padding: 8px 24px;
                margin: 0 8px;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s;

                &.btn-primary {
                  background: #1890ff;
                  color: #fff;

                  &:hover {
                    background: #40a9ff;
                  }
                }

                &.btn-default {
                  background: #f5f5f5;
                  color: #262626;
                  border: 1px solid #d9d9d9;

                  &:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                  }
                }
              }
            }
          }
        }
      }

      // 右侧属性面板
      .properties-panel {
        width: 300px;
        height: 100%;
        border-left: 1px solid #e8e8e8;
        background: #fafafa;
        overflow: auto;
        flex-shrink: 0;

        .panel-header {
          padding: 12px 16px;
          border-bottom: 1px solid #e8e8e8;
          background: #fff;

          h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }
        }

        .panel-body {
          padding: 16px;

          .property-tabs {
            .tab-headers {
              display: flex;
              border-bottom: 1px solid #e8e8e8;
              margin-bottom: 16px;

              .tab-header {
                padding: 8px 12px;
                font-size: 12px;
                color: #8c8c8c;
                cursor: pointer;
                border-bottom: 2px solid transparent;
                transition: all 0.3s;

                &:hover {
                  color: #1890ff;
                }

                &.active {
                  color: #1890ff;
                  border-bottom-color: #1890ff;
                }
              }
            }

            .tab-content {
              .property-group {
                .property-item {
                  margin-bottom: 16px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  label {
                    display: block;
                    margin-bottom: 4px;
                    font-size: 12px;
                    font-weight: 500;
                    color: #262626;

                    input[type="checkbox"] {
                      margin-right: 6px;
                      width: auto;
                    }
                  }

                  input, textarea, select {
                    width: 100%;
                    padding: 6px 8px;
                    border: 1px solid #d9d9d9;
                    border-radius: 4px;
                    font-size: 12px;
                    transition: border-color 0.3s;

                    &:focus {
                      border-color: #1890ff;
                      outline: none;
                      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    }
                  }

                  textarea {
                    resize: vertical;
                    min-height: 60px;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 加载遮罩
    .loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  }

  // 底部状态栏
  .form-statusbar {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;
    font-size: 12px;
    color: #8c8c8c;

    .statusbar-left,
    .statusbar-right {
      display: flex;
      align-items: center;
    }

    .status-item {
      display: flex;
      align-items: center;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }

      i {
        margin-right: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .form-editor-embedded {
    .form-main-content {
      .components-panel {
        width: 200px;
      }

      .properties-panel {
        width: 250px;
      }
    }
  }
}

@media (max-width: 768px) {
  .form-editor-embedded {
    .form-toolbar {
      flex-direction: column;
      height: auto;
      padding: 8px 16px;

      .toolbar-left,
      .toolbar-right {
        margin: 4px 0;
      }
    }

    .form-main-content {
      flex-direction: column;

      .components-panel,
      .properties-panel {
        width: 100%;
        height: 200px;
        border: none;
        border-bottom: 1px solid #e8e8e8;
      }

      .form-design-area {
        .form-canvas {
          margin: 8px;
        }
      }
    }

    .form-statusbar {
      flex-direction: column;
      height: auto;
      padding: 8px 16px;

      .statusbar-left,
      .statusbar-right {
        width: 100%;
        justify-content: center;
        margin: 2px 0;
      }
    }
  }
}
