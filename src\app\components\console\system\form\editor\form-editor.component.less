// form-js 表单编辑器样式
.form-editor-wrapper {
  height: calc(100vh - 20px);
  min-height: 600px;
  background: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;

  // 顶部工具栏
  .form-toolbar {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 8px;
        
        &:hover {
          color: #1890ff;
        }
      }

      .divider {
        margin: 0 12px;
        color: #d9d9d9;
        font-size: 14px;
      }

      .form-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  // 编辑器容器
  .form-editor-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 500px;

    &.loading {
      pointer-events: none;
    }

    // form-js 容器
    .form-js-container {
      width: 100%;
      height: 100%;
      background: #fff;
    }

    // 加载遮罩
    .loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
  }
}

// form-js 样式覆盖
:host ::ng-deep {
  // form-js 编辑器样式
  .fjs-container {
    height: 100% !important;
    border: none !important;
  }

  .fjs-editor {
    height: 100% !important;
  }

  .fjs-palette {
    background: #fafafa !important;
    border-right: 1px solid #e8e8e8 !important;
  }

  .fjs-properties-panel {
    background: #fafafa !important;
    border-left: 1px solid #e8e8e8 !important;
  }

  .fjs-canvas {
    background: #f5f5f5 !important;
  }

  .fjs-form {
    background: #fff !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    margin: 20px !important;
    padding: 20px !important;
  }

  // 调整组件面板样式
  .fjs-palette-container {
    .fjs-palette-group {
      margin-bottom: 16px;

      .fjs-palette-group-header {
        font-size: 12px;
        font-weight: 500;
        color: #8c8c8c;
        text-transform: uppercase;
        padding: 8px 12px;
        background: #f0f0f0;
        border-radius: 4px;
        margin-bottom: 8px;
      }

      .fjs-palette-entry {
        margin-bottom: 4px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background: #e6f7ff;
          border-color: #1890ff;
        }
      }
    }
  }

  // 调整属性面板样式
  .fjs-properties-panel-container {
    .fjs-properties-panel-header {
      background: #fff;
      border-bottom: 1px solid #e8e8e8;
      padding: 12px 16px;

      .fjs-properties-panel-header-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .fjs-properties-panel-content {
      padding: 16px;

      .fjs-properties-panel-group {
        margin-bottom: 16px;

        .fjs-properties-panel-group-header {
          font-size: 12px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 8px;
        }

        .fjs-properties-panel-entry {
          margin-bottom: 12px;

          label {
            font-size: 12px;
            color: #262626;
            margin-bottom: 4px;
          }

          input, textarea, select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;

            &:focus {
              border-color: #1890ff;
              outline: none;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
          }
        }
      }
    }
  }

  // 调整工具栏样式
  .fjs-toolbar {
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;

    .fjs-toolbar-group {
      .fjs-toolbar-entry {
        margin-right: 8px;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          background: #f0f0f0;
        }

        &.fjs-toolbar-entry-active {
          background: #e6f7ff;
          color: #1890ff;
        }
      }
    }
  }
}
