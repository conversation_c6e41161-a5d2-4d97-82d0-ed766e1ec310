import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzTableQueryParams } from 'ng-zorro-antd/table';

// 表单状态枚举
export const FormStatusMap = {
    'draft': '草稿',
    'published': '已发布',
    'suspended': '已暂停',
    'archived': '已归档'
};

@Component({
    selector: 'app-form',
    templateUrl: './form.component.html',
    styleUrls: ['./form.component.less']
})
export class FormComponent implements OnInit {
    constructor(
        private router: Router,
        private msg: NzMessageService,
        private modal: NzModalService
    ) {}

    isLoading: boolean = false;
    keyword: string = '';
    tableData = [];
    busyStatus = {};

    // 配置弹窗相关
    configModalVisible: boolean = false;
    editData: any = null;
    
    // 分页配置
    pager = {
        page: 1,
        pageSize: 10,
        total: 0
    };

    // 查询过滤条件
    filters = {
        pageNum: 0,
        pageSize: 10,
        orderBy1: '',
        orderName1: ''
    };

    orderBy1: '';
    orderName1: '';

    ngOnInit(): void {
        this.getDataList();
    }

    // 获取数据列表
    getDataList() {
        this.isLoading = true;
        const params = {
            ...this.filters,
        };

        // 模拟数据，实际项目中应该调用表单服务
        setTimeout(() => {
            this.isLoading = false;
            this.tableData = [
                {
                    id: 1,
                    name: '用户注册表单',
                    formKey: 'user_register',
                    version: '1.0',
                    status: 'published',
                    createTime: '2024-01-15 10:30:00',
                    updateTime: '2024-01-20 14:20:00',
                    description: '用户注册信息收集表单'
                },
                {
                    id: 2,
                    name: '意见反馈表单',
                    formKey: 'feedback',
                    version: '1.2',
                    status: 'draft',
                    createTime: '2024-01-10 09:15:00',
                    updateTime: '2024-01-18 16:45:00',
                    description: '收集用户意见和建议'
                }
            ];
            this.pager.total = this.tableData.length;
        }, 1000);
    }

    // 搜索
    search() {
        this.pager.page = 1;
        this.filters.pageNum = 0;
        this.getDataList();
    }

    // 分页变化
    pageChanged(page: number) {
        this.pager.page = page;
        this.filters.pageNum = page - 1;
        this.getDataList();
    }

    // 表格参数变化
    onParamsChange(params: NzTableQueryParams) {
        const { pageSize, pageIndex, sort } = params;
        const currentSort = sort.find(item => item.value !== null);
        
        this.pager.pageSize = pageSize;
        this.pager.page = pageIndex;
        this.filters.pageSize = pageSize;
        this.filters.pageNum = pageIndex - 1;
        
        if (currentSort) {
            this.filters.orderName1 = currentSort.key;
            this.filters.orderBy1 = currentSort.value === 'ascend' ? 'true' : 'false';
        }
        
        this.getDataList();
    }

    // 新增表单
    addForm() {
        // 跳转到表单设计器页面
        this.router.navigate(['/console/system/form-editor']);
    }

    // 编辑表单
    editForm(data: any) {
        // 跳转到表单设计器页面，传递表单ID
        this.router.navigate(['/console/system/form-editor', data.id]);
    }

    // 设计表单
    designForm(data: any) {
        // 跳转到表单设计器页面进行表单设计
        this.router.navigate(['/console/system/form-editor', data.id]);
    }

    // 查看表单
    viewForm(data: any) {
        // 跳转到查看页面
        this.router.navigate(['/console/system/form-view', data.id]);
    }

    // 删除表单
    deleteForm(data: any) {
        this.modal.confirm({
            nzTitle: '确认删除',
            nzContent: `确定要删除表单"${data.name}"吗？`,
            nzOnOk: () => {
                this.busyStatus[data.id] = true;
                // 模拟删除操作
                setTimeout(() => {
                    this.busyStatus[data.id] = false;
                    this.msg.success('删除成功');
                    this.getDataList();
                }, 1000);
            }
        });
    }

    // 发布表单
    publishForm(data: any) {
        this.busyStatus[data.id] = true;
        // 模拟发布操作
        setTimeout(() => {
            this.busyStatus[data.id] = false;
            this.msg.success('发布成功');
            this.getDataList();
        }, 1000);
    }

    // 暂停表单
    suspendForm(data: any) {
        this.busyStatus[data.id] = true;
        // 模拟暂停操作
        setTimeout(() => {
            this.busyStatus[data.id] = false;
            this.msg.success('暂停成功');
            this.getDataList();
        }, 1000);
    }

    // 获取表单状态显示文本
    getFormStatus(status: string): string {
        return FormStatusMap[status] || status;
    }

    // 格式化日期
    formatDate(date: string): string {
        if (!date) return '-';
        return new Date(date).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 跟踪函数
    trackById(index: number, item: any): any {
        return item.id;
    }

    // 配置保存回调
    onConfigSave() {
        this.getDataList();
    }
}
