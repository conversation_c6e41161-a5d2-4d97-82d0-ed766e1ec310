import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormEditor } from '@bpmn-io/form-js';

@Component({
    selector: 'app-form-editor',
    templateUrl: './form-editor.component.html',
    styleUrls: ['./form-editor.component.less']
})
export class FormEditorComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('formContainer', { static: true }) formContainer!: ElementRef;

    public formId: string | null = null;
    public formData: any = null;
    public loading = false;
    public saving = false;

    // form-js 编辑器实例
    private formEditor: FormEditor | null = null;

    // 默认表单 schema
    private defaultSchema = {
        type: 'default',
        id: 'Form_1xzopdu',
        components: [
            {
                text: '# File an Invoice\n\nAdd your invoice details below.',
                type: 'text',
                id: 'Field_0plkx1b'
            },
            {
                key: 'textField1',
                label: 'Text field',
                type: 'textfield',
                id: 'Field_1d7zk8x',
                validate: {
                    required: false
                }
            },
            {
                key: 'textField2',
                label: 'Text field',
                type: 'textfield',
                id: 'Field_1h9k7m2',
                validate: {
                    required: false
                }
            },
            {
                action: 'submit',
                key: 'submit',
                label: 'Submit',
                type: 'button',
                id: 'Field_1jz8k3l'
            }
        ]
    };

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.formId = this.route.snapshot.paramMap.get('id');

        if (this.formId) {
            this.loadFormData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initFormEditor();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.formEditor) {
            this.formEditor.destroy();
            this.formEditor = null;
        }
    }

    // 加载表单数据
    private async loadFormData(): Promise<void> {
        if (!this.formId) return;

        try {
            this.loading = true;

            // 模拟加载表单数据
            setTimeout(() => {
                this.loading = false;
                this.formData = {
                    id: this.formId,
                    name: '用户注册表单',
                    formKey: 'user_register',
                    version: '1.0',
                    status: 'draft',
                    schema: this.defaultSchema
                };

                // 如果表单编辑器已初始化，加载 schema
                if (this.formEditor) {
                    this.loadFormSchema(this.formData.schema);
                }
            }, 1000);

        } catch (error) {
            console.error('加载表单数据失败:', error);
            this.msg.error('加载表单数据失败');
            this.loading = false;
        }
    }

    // 初始化 form-js 编辑器
    private async initFormEditor(): Promise<void> {
        if (!this.formContainer) {
            return;
        }

        try {
            this.formEditor = new FormEditor({
                container: this.formContainer.nativeElement
            });

            // 导入表单 schema
            const schema = this.formData?.schema || this.defaultSchema;
            await this.formEditor.importSchema(schema);

            // 监听表单变化事件
            this.formEditor.on('changed', (event) => {
                console.log('Form changed:', event);
            });

            this.msg.success('表单编辑器初始化成功');

        } catch (error) {
            console.error('初始化表单编辑器失败:', error);
            this.msg.error('初始化表单编辑器失败');
        }
    }

    // 加载表单 schema
    private async loadFormSchema(schema: any): Promise<void> {
        if (this.formEditor) {
            try {
                await this.formEditor.importSchema(schema);
            } catch (error) {
                console.error('加载表单 schema 失败:', error);
                this.msg.error('加载表单 schema 失败');
            }
        }
    }

    // 保存表单
    async save(): Promise<void> {
        if (!this.formEditor) {
            this.msg.error('表单编辑器未初始化');
            return;
        }

        try {
            this.saving = true;

            // 获取当前表单 schema
            const schema = this.formEditor.getSchema();

            // 准备保存数据
            const saveData = {
                id: this.formId,
                schema: schema,
                name: this.formData?.name || '新建表单',
                description: this.formData?.description || ''
            };

            console.log('保存表单数据:', saveData);

            // 模拟保存操作
            setTimeout(() => {
                this.saving = false;
                this.msg.success('保存成功');
            }, 1000);

        } catch (error) {
            console.error('保存表单失败:', error);
            this.msg.error('保存表单失败');
            this.saving = false;
        }
    }

    // 下载表单配置
    async downloadConfig(): Promise<void> {
        if (!this.formEditor) {
            this.msg.error('表单编辑器未初始化');
            return;
        }

        try {
            const schema = this.formEditor.getSchema();
            const configJson = JSON.stringify(schema, null, 2);

            const blob = new Blob([configJson], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.formData?.name || 'form'}.json`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载表单配置失败:', error);
            this.msg.error('下载表单配置失败');
        }
    }

    // 预览表单
    previewForm(): void {
        if (!this.formEditor) {
            this.msg.error('表单编辑器未初始化');
            return;
        }

        try {
            const schema = this.formEditor.getSchema();

            // 在新窗口中预览表单
            const previewHtml = this.generatePreviewHtml(schema);
            const newWindow = window.open('', '_blank');
            if (newWindow) {
                newWindow.document.write(previewHtml);
                newWindow.document.close();
            }
        } catch (error) {
            console.error('预览表单失败:', error);
            this.msg.error('预览表单失败');
        }
    }

    // 生成预览HTML
    private generatePreviewHtml(schema: any): string {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>表单预览</title>
                <meta charset="utf-8">
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        padding: 20px;
                        background: #f5f5f5;
                    }
                    .form-preview {
                        max-width: 600px;
                        margin: 0 auto;
                        background: white;
                        padding: 20px;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                </style>
                <script src="https://unpkg.com/@bpmn-io/form-js@1.9.1/dist/index.umd.js"></script>
            </head>
            <body>
                <div class="form-preview">
                    <div id="form-container"></div>
                </div>
                <script>
                    const { Form } = FormJS;
                    const schema = ${JSON.stringify(schema)};

                    const form = new Form({
                        container: document.getElementById('form-container')
                    });

                    form.importSchema(schema).catch(err => {
                        console.error('Failed to import form schema', err);
                    });
                </script>
            </body>
            </html>
        `;
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/form']);
    }
}
