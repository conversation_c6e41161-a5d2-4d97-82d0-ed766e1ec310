import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
    selector: 'app-form-editor',
    templateUrl: './form-editor.component.html',
    styleUrls: ['./form-editor.component.less']
})
export class FormEditorComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('formContainer', { static: false }) formContainer!: ElementRef;
    @ViewChild('propertiesPanel', { static: false }) propertiesPanel!: ElementRef;

    public formId: string | null = null;
    public formData: any = null;
    public loading = false;
    public saving = false;

    // 表单设计器实例
    private formDesigner: any = null;

    // 默认表单配置
    private defaultFormConfig = {
        title: '新建表单',
        description: '',
        fields: [
            {
                type: 'input',
                label: '姓名',
                name: 'name',
                required: true,
                placeholder: '请输入姓名'
            },
            {
                type: 'email',
                label: '邮箱',
                name: 'email',
                required: true,
                placeholder: '请输入邮箱地址'
            },
            {
                type: 'textarea',
                label: '备注',
                name: 'remark',
                required: false,
                placeholder: '请输入备注信息'
            }
        ]
    };

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private msg: NzMessageService
    ) {}

    ngOnInit(): void {
        // 获取路由参数
        this.formId = this.route.snapshot.paramMap.get('id');

        if (this.formId) {
            this.loadFormData();
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.initFormDesigner();
        }, 100);
    }

    ngOnDestroy(): void {
        if (this.formDesigner) {
            // 清理表单设计器资源
            this.formDesigner = null;
        }
    }

    // 加载表单数据
    private async loadFormData(): Promise<void> {
        if (!this.formId) return;

        try {
            this.loading = true;
            
            // 模拟加载表单数据
            setTimeout(() => {
                this.loading = false;
                this.formData = {
                    id: this.formId,
                    name: '用户注册表单',
                    formKey: 'user_register',
                    version: '1.0',
                    status: 'draft',
                    config: this.defaultFormConfig
                };

                // 如果表单设计器已初始化，加载配置
                if (this.formDesigner) {
                    this.loadFormConfig(this.formData.config);
                }
            }, 1000);

        } catch (error) {
            console.error('加载表单数据失败:', error);
            this.msg.error('加载表单数据失败');
            this.loading = false;
        }
    }

    // 初始化表单设计器
    private initFormDesigner(): void {
        if (!this.formContainer) {
            return;
        }

        try {
            // 这里应该初始化实际的表单设计器
            // 由于没有具体的表单设计器库，这里使用模拟实现
            this.formDesigner = {
                container: this.formContainer.nativeElement,
                config: this.formData?.config || this.defaultFormConfig
            };

            // 渲染表单预览
            this.renderFormPreview();

            this.msg.success('表单设计器初始化成功');

        } catch (error) {
            console.error('初始化表单设计器失败:', error);
            this.msg.error('初始化表单设计器失败');
        }
    }

    // 渲染表单预览
    private renderFormPreview(): void {
        if (!this.formContainer || !this.formDesigner) return;

        const config = this.formDesigner.config;
        const container = this.formContainer.nativeElement;

        // 清空容器
        container.innerHTML = '';

        // 创建表单预览HTML
        const formHtml = `
            <div class="form-preview">
                <div class="form-header">
                    <h3>${config.title}</h3>
                    <p>${config.description}</p>
                </div>
                <div class="form-body">
                    ${config.fields.map(field => this.renderField(field)).join('')}
                </div>
                <div class="form-footer">
                    <button type="button" class="btn btn-primary">提交</button>
                    <button type="button" class="btn btn-default">重置</button>
                </div>
            </div>
        `;

        container.innerHTML = formHtml;
    }

    // 渲染表单字段
    private renderField(field: any): string {
        const required = field.required ? '<span class="required">*</span>' : '';
        
        switch (field.type) {
            case 'input':
                return `
                    <div class="form-field">
                        <label>${field.label}${required}</label>
                        <input type="text" placeholder="${field.placeholder}" />
                    </div>
                `;
            case 'email':
                return `
                    <div class="form-field">
                        <label>${field.label}${required}</label>
                        <input type="email" placeholder="${field.placeholder}" />
                    </div>
                `;
            case 'textarea':
                return `
                    <div class="form-field">
                        <label>${field.label}${required}</label>
                        <textarea placeholder="${field.placeholder}" rows="3"></textarea>
                    </div>
                `;
            case 'select':
                return `
                    <div class="form-field">
                        <label>${field.label}${required}</label>
                        <select>
                            <option value="">请选择</option>
                            ${(field.options || []).map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('')}
                        </select>
                    </div>
                `;
            default:
                return '';
        }
    }

    // 加载表单配置
    private loadFormConfig(config: any): void {
        if (this.formDesigner) {
            this.formDesigner.config = config;
            this.renderFormPreview();
        }
    }

    // 保存表单
    async save(): Promise<void> {
        try {
            this.saving = true;

            // 获取表单配置
            const formConfig = this.formDesigner?.config || this.defaultFormConfig;

            // 准备保存数据
            const saveData = {
                id: this.formId,
                config: formConfig,
                name: formConfig.title,
                description: formConfig.description
            };

            // 模拟保存操作
            setTimeout(() => {
                this.saving = false;
                this.msg.success('保存成功');
            }, 1000);

        } catch (error) {
            console.error('保存表单失败:', error);
            this.msg.error('保存表单失败');
            this.saving = false;
        }
    }

    // 下载表单配置
    async downloadConfig(): Promise<void> {
        try {
            const config = this.formDesigner?.config || this.defaultFormConfig;
            const configJson = JSON.stringify(config, null, 2);

            const blob = new Blob([configJson], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.formData?.name || 'form'}.json`;
            link.click();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('下载表单配置失败:', error);
            this.msg.error('下载表单配置失败');
        }
    }

    // 预览表单
    previewForm(): void {
        // 在新窗口中预览表单
        const config = this.formDesigner?.config || this.defaultFormConfig;
        const previewHtml = this.generatePreviewHtml(config);
        
        const newWindow = window.open('', '_blank');
        if (newWindow) {
            newWindow.document.write(previewHtml);
            newWindow.document.close();
        }
    }

    // 生成预览HTML
    private generatePreviewHtml(config: any): string {
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>${config.title}</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    .form-preview { max-width: 600px; margin: 0 auto; }
                    .form-field { margin-bottom: 15px; }
                    label { display: block; margin-bottom: 5px; font-weight: bold; }
                    input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
                    .required { color: red; }
                    .btn { padding: 10px 20px; margin-right: 10px; border: none; border-radius: 4px; cursor: pointer; }
                    .btn-primary { background: #1890ff; color: white; }
                    .btn-default { background: #f5f5f5; color: #333; }
                </style>
            </head>
            <body>
                ${this.formContainer?.nativeElement.innerHTML || ''}
            </body>
            </html>
        `;
    }

    // 返回列表
    goBack(): void {
        this.router.navigate(['/console/system/form']);
    }
}
