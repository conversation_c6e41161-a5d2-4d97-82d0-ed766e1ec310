{"name": "aic-webui", "version": "0.0.1", "scripts": {"build": "ng build --configuration production", "start": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng serve --port 4200 --proxy-config proxy.conf.js", "start:json": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng serve --port 4200 --proxy-config proxy.conf.json", "start:custom": "node --max-old-space-size=4096 server.js", "start:express": "ng build && node --max-old-space-size=4096 custom-server.js", "start:fixed": "node fix-event-emitter.js && node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng serve --port 4200 --proxy-config proxy.conf.js", "clean": "rimraf node_modules", "lint": "ng lint"}, "husky": {"hooks": {}}, "lint-staged": {"src/**/*.ts": ["ng-lint-staged lint:app --fix --", "git add"], "e2e/**/*.ts": ["ng-lint-staged lint:e2e --fix --", "git add"]}, "private": true, "engines": {"node": ">=18.13.0"}, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@bpmn-io/form-js": "^1.16.1-alpha.0", "@ctrl/tinycolor": "^4.1.0", "@types/clipboard": "^2.0.1", "@types/highlight.js": "^9.12.3", "@types/marked": "^0.7.2", "bootstrap": "^4.0.0-alpha.6", "bpmn-js": "^18.6.2", "bpmn-js-i18n-zh": "^1.3.0", "bpmn-js-properties-panel": "^5.38.1", "camunda-bpmn-moddle": "^7.0.1", "clipboard": "^2.0.4", "core-js": "^3.21.1", "date-fns": "^2.14.0", "echarts": "^5.4.3", "file-saver": "^2.0.0", "highlight.js": "^9.17.0", "http-proxy-middleware": "^3.0.5", "jquery": "^3.5.1", "js-base64": "^3.7.7", "json2yaml": "^1.1.0", "marked": "^4.0.10", "ng-zorro-antd": "^18.2.1", "ngx-echarts": "^8.0.1", "ngx-filesaver": "^18.0.0", "ngx-pagination": "^6.0.3", "resize-observer-polyfill": "^1.5.1", "rxjs": "^7.8.1", "tslib": "^2.6.2", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.13", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^18.2.13", "@angular/compiler-cli": "^18.2.13", "@angular/language-service": "^18.2.13", "@angularclass/hmr": "^2.1.3", "@types/echarts": "^4.1.3", "@types/jasmine": "~3.10.0", "@types/jasminewd2": "~2.0.10", "@types/node": "^12.20.55", "@types/zrender": "^5.0.0", "codelyzer": "^6.0.2", "eslint": "^9.25.1", "eslint-plugin-import": "^2.31.0", "husky": "^2.2.0", "jasmine-core": "~4.0.0", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "lint-staged": "^8.1.6", "ng-lint-staged": "^0.1.6", "prettier": "^1.17.0", "protractor": "~7.0.0", "rimraf": "^3.0.2", "ts-node": "~7.0.0", "typescript": "~5.4.5", "webpack": "^5.76.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.15.1"}}