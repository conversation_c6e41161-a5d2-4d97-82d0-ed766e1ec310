import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ManagementComponent } from 'src/app/components/console/system/management/management.component';
import { AccountComponent } from 'src/app/components/console/system/account/account.component';
import { OrderComponent } from 'src/app/components/console/system/order/order.component';
import { IndexComponent as PermissionIndexComponent } from 'src/app/components/console/system/permission/index/index.component';
import { QuotaComponent } from 'src/app/components/console/system/quota/quota.component';
import { UserTemplateComponent } from 'src/app/components/console/system/quota/user-template/user-template.component';
import { DetailComponent } from 'src/app/components/console/system/quota/detail/detail.component';
import { AppSystemQuotaComponent } from 'src/app/components/console/system/quota/app-system-quota/app-system-quota.component';
import { CloudAuditComponent } from 'src/app/components/console/monitor-manager/cloud-audit/cloud-audit.component';
import { IndexComponent as ServicePlanIndexComponent } from 'src/app/components/console/service-plan/index/index.component';
import { ServicePlanComponent } from 'src/app/components/console/system/servicePlan/service-plan.component';
import { BpmnComponent } from 'src/app/components/console/system/bpmn/bpmn.component';
import { BpmnEditorComponent } from 'src/app/components/console/system/bpmn/editor/bpmn-editor.component';
import { BpmnViewComponent } from 'src/app/components/console/system/bpmn/view/bpmn-view.component';
import { FormComponent } from 'src/app/components/console/system/form/form.component';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'index',
        pathMatch: 'full',
    },
    {
        path: 'index',
        component: ManagementComponent,
    },
    {
        path: 'account',
        component: AccountComponent,
    },
    {
        path: 'order',
        component: OrderComponent,
    },
    {
        path: 'permission',
        component: PermissionIndexComponent,
    },
    {
        path: 'quota',
        component: QuotaComponent,
    },
    {
        path: 'user-template',
        component: UserTemplateComponent,
    },
    {
        path: 'detail/:id',
        component: DetailComponent,
    },
    {
        path: 'app-system-quota/:id',
        component: AppSystemQuotaComponent,
    },
    {
        path: 'cloud-audit',
        component: CloudAuditComponent,
    },
    {
        path: 'service-plan',
        component: ServicePlanComponent,
    },
    {
        path: 'vdi-pool',
        loadChildren: () => import('../vdi/vdi.module').then(m => m.VdiModule),
    },
    {
        path: 'bpmn',
        component: BpmnComponent,
    },
    {
        path: 'bpmn-editor',
        component: BpmnEditorComponent,
    },
    {
        path: 'bpmn-editor/:id',
        component: BpmnEditorComponent,
    },
    {
        path: 'bpmn-view/:id',
        component: BpmnViewComponent,
    },
    {
        path: 'form',
        component: FormComponent,
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SystemRoutingModule {}
